import React from 'react';
import '../styles/IngredientTable.css';
import { formatSeasonality } from '../../../components/SeasonalityFormatter';
import EditableCell from './EditableCell';

const IngredientTable = ({ 
  filteredIngredients, 
  typeOptions, 
  measurementOptions,
  formatDivisibleBy,
  editMode,
  editValues,
  saveStatus,
  toggleEditMode,
  handleInputChange,
  saveChanges,
  cancelEdit
}) => {
  return (
    <div className="table-responsive">
      <table className="ingredients-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Type</th>
            <th>Measurement</th>
            <th>Divisible By</th>
            <th>Bought By</th>
            <th>Bought Amount</th>
            <th>Needs Calculating</th>
            <th>Seasonality</th>
          </tr>
        </thead>
        <tbody>
          {filteredIngredients.map(ingredient => (
            <tr key={ingredient.id}>
              <td>{ingredient.id}</td>
              <EditableCell
                ingredient={ingredient}
                field="name"
                content={<strong>{ingredient.name}</strong>}
                typeOptions={typeOptions}
                measurementOptions={measurementOptions}
                editMode={editMode}
                editValues={editValues}
                saveStatus={saveStatus}
                toggleEditMode={toggleEditMode}
                handleInputChange={handleInputChange}
                saveChanges={saveChanges}
                cancelEdit={cancelEdit}
              />
              <EditableCell
                ingredient={ingredient}
                field="type"
                content={<span className={`type-cell type-${ingredient.type}`}>{ingredient.type}</span>}
                typeOptions={typeOptions}
                measurementOptions={measurementOptions}
                editMode={editMode}
                editValues={editValues}
                saveStatus={saveStatus}
                toggleEditMode={toggleEditMode}
                handleInputChange={handleInputChange}
                saveChanges={saveChanges}
                cancelEdit={cancelEdit}
              />
              <EditableCell
                ingredient={ingredient}
                field="divisible_by_measurement"
                content={ingredient.divisible_by_measurement}
                typeOptions={typeOptions}
                measurementOptions={measurementOptions}
                editMode={editMode}
                editValues={editValues}
                saveStatus={saveStatus}
                toggleEditMode={toggleEditMode}
                handleInputChange={handleInputChange}
                saveChanges={saveChanges}
                cancelEdit={cancelEdit}
              />
              <EditableCell
                ingredient={ingredient}
                field="divisible_by_int"
                content={formatDivisibleBy(ingredient)}
                typeOptions={typeOptions}
                measurementOptions={measurementOptions}
                editMode={editMode}
                editValues={editValues}
                saveStatus={saveStatus}
                toggleEditMode={toggleEditMode}
                handleInputChange={handleInputChange}
                saveChanges={saveChanges}
                cancelEdit={cancelEdit}
              />
              <EditableCell
                ingredient={ingredient}
                field="bought_by"
                content={ingredient.bought_by ?? 'N/A'}
                typeOptions={typeOptions}
                measurementOptions={measurementOptions}
                editMode={editMode}
                editValues={editValues}
                saveStatus={saveStatus}
                toggleEditMode={toggleEditMode}
                handleInputChange={handleInputChange}
                saveChanges={saveChanges}
                cancelEdit={cancelEdit}
              />
              <EditableCell
                ingredient={ingredient}
                field="bought_by_amount"
                content={ingredient.bought_by_amount ?? 'N/A'}
                typeOptions={typeOptions}
                measurementOptions={measurementOptions}
                editMode={editMode}
                editValues={editValues}
                saveStatus={saveStatus}
                toggleEditMode={toggleEditMode}
                handleInputChange={handleInputChange}
                saveChanges={saveChanges}
                cancelEdit={cancelEdit}
              />
              <EditableCell
                ingredient={ingredient}
                field="needs_calculating"
                content={ingredient.needs_calculating ? 'Yes' : 'No'}
                typeOptions={typeOptions}
                measurementOptions={measurementOptions}
                editMode={editMode}
                editValues={editValues}
                saveStatus={saveStatus}
                toggleEditMode={toggleEditMode}
                handleInputChange={handleInputChange}
                saveChanges={saveChanges}
                cancelEdit={cancelEdit}
              />
              <td className="seasonality-cell">
                {formatSeasonality(ingredient)}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default IngredientTable; 