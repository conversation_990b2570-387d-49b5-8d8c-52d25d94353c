import { useState } from 'react';
import { formatSeasonality } from '../../../components/SeasonalityFormatter';
import { useIngredients as useSharedIngredients } from '../../../hooks/useIngredients';

export const useIngredients = () => {
  const { ingredients, setIngredients, fetchIngredients } = useSharedIngredients();
  const [activeTypeTab, setActiveTypeTab] = useState('all');
  
  // Options for dropdown menus
  const typeOptions = ['all', 'vegetable', 'herb', 'meat', 'dairy', 'oil', 'spice', 'grain', 'preserve'];
  const measurementOptions = ['unit', 'g', 'ml', 'tbsp', 'tsp', 'cup', 'sprig'];

  // Filter ingredients based on selected type
  const filteredIngredients = activeTypeTab === 'all' 
    ? ingredients 
    : ingredients.filter(item => item.type === activeTypeTab);
  
  // Parse divisible_by_int to display properly
  const formatDivisibleBy = (ingredient) => {
    try {
      if (typeof ingredient.divisible_by_int === 'string') {
        return JSON.parse(ingredient.divisible_by_int).join(', ');
      } else if (Array.isArray(ingredient.divisible_by_int)) {
        return ingredient.divisible_by_int.join(', ');
      }
      return 'N/A';
    } catch (e) {
      return '-';
    }
  };

  return {
    ingredients,
    setIngredients,
    activeTypeTab,
    setActiveTypeTab,
    filteredIngredients,
    typeOptions,
    measurementOptions,
    formatDivisibleBy,
    fetchIngredients,
    formatSeasonality
  };
}; 