import React from 'react';

// Import global styles
import '../../styles/IngredientData.css';
import '../../styles/MonthSelection.css';
// Import page-specific styles
import './styles/index.css';

// Import page-specific hooks
import { useIngredients } from './hooks/useIngredients';
import { useEditMode } from './hooks/useEditMode';

// Import page-specific components
import IngredientHeader from './components/IngredientHeader';
import TypeTabBar from './components/TypeTabBar';
import IngredientTable from './components/IngredientTable';
import IngredientAdder from './components/IngredientAdder/index'; // Import the new component

function Ingredient() {
  // Get ingredients-related state and handlers
  const {
    ingredients,
    setIngredients,
    filteredIngredients,
    activeTypeTab,
    setActiveTypeTab,
    typeOptions,
    measurementOptions,
    formatDivisibleBy,
    formatBoughtByAmount
  } = useIngredients();
  
  // Get edit-related state and handlers
  const {
    editMode,
    editValues,
    saveStatus,
    toggleEditMode,
    handleInputChange,
    saveChanges,
    cancelEdit
  } = useEditMode(ingredients, setIngredients);
  
  return (
    <div className="ingredient-container">
      <IngredientHeader />
      <IngredientAdder /> {/* Add the new component here */}
      
      <div className="ingredient-list-container">
        {ingredients.length === 0 ? (
          <p>Loading ingredients...</p>
        ) : (
          <div className="ingredient-list">
            <TypeTabBar 
              typeOptions={typeOptions} 
              activeTypeTab={activeTypeTab} 
              setActiveTypeTab={setActiveTypeTab} 
            />
            
            <IngredientTable
              filteredIngredients={filteredIngredients}
              typeOptions={typeOptions}
              measurementOptions={measurementOptions}
              formatDivisibleBy={formatDivisibleBy}
              formatBoughtByAmount={formatBoughtByAmount}
              editMode={editMode}
              editValues={editValues}
              saveStatus={saveStatus}
              toggleEditMode={toggleEditMode}
              handleInputChange={handleInputChange}
              saveChanges={saveChanges}
              cancelEdit={cancelEdit}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default Ingredient; 
