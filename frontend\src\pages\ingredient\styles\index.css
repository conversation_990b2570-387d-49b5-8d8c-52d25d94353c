/* Ingredient page specific styling */
.ingredient-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.ingredient-list-container {
  width: 100%;
  max-width: 1200px;
  margin: 20px auto;
  padding: 20px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.ingredient-list {
  width: 100%;
}

/* Remove any nested .table-container margins */
.ingredient-list .table-container {
  margin: 0;
  border-radius: 8px;
  box-shadow: none;
  padding: 0;
}

.table-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.table-responsive {
  overflow-x: auto;
}

/* Improved header styling */
.ingredient-header {
  width: 100%;
  max-width: 1200px;
  text-align: left;
  margin-bottom: 25px;
  padding: 15px;
  background-color: #2a9d8f;
  color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ingredient-header h1 {
  font-size: 2.5rem;
  margin-bottom: 5px;
  color: white;
}

.ingredient-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 0;
}

/* Type tabs styling */
.type-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.type-tab {
  padding: 8px 16px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.type-tab.active {
  background-color: #6a994e;
  color: white;
  border-color: #386641;
}

/* Table styling */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
}

th {
  background-color: #f0f7ff;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e0e0e0;
}

td {
  padding: 10px 15px;
  border-bottom: 1px solid #e0e0e0;
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background-color: #f9f9f9;
}

/* Animation for row transitions */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

tr {
  animation: fadeIn 0.3s ease;
}

/* Edit and action buttons */
.edit-btn,
.save-btn,
.cancel-btn {
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-right: 5px;
}

.edit-btn {
  background-color: #2196f3;
  color: white;
}

.edit-btn:hover {
  background-color: #1976d2;
}

.save-btn {
  background-color: #4caf50;
  color: white;
}

.save-btn:hover {
  background-color: #388e3c;
}

.cancel-btn {
  background-color: #f44336;
  color: white;
}

.cancel-btn:hover {
  background-color: #d32f2f;
} 