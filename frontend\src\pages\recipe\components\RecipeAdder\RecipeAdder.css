/* Styling for the RecipeAdder component */
.recipe-adder-container {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.recipe-adder-container h2 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
}

.recipe-adder-form .form-section {
  background-color: #fff;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #eee;
}

.recipe-adder-form .form-section h3 {
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.recipe-adder-form .form-group {
  margin-bottom: 15px;
}

.recipe-adder-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #444;
}

.recipe-adder-form input[type="text"],
.recipe-adder-form input[type="number"],
.recipe-adder-form textarea,
.recipe-adder-form select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 1rem;
}

.recipe-adder-form input[type="file"] {
  padding: 3px;
}

.recipe-adder-form textarea {
  resize: vertical;
}

.recipe-adder-form .image-preview {
  max-width: 200px;
  max-height: 200px;
  margin-top: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.recipe-adder-form .months-selection {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 5px;
}

.recipe-adder-form .month-button {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.recipe-adder-form .month-button.selected {
  background-color: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.recipe-adder-form .month-button:hover:not(.selected) {
  background-color: #e0e0e0;
}

.recipe-adder-form input[type="checkbox"] {
  margin-right: 5px;
  vertical-align: middle;
}

.ingredient-search-results {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
  margin-bottom: 15px;
  max-height: 150px; /* Limit height and make scrollable if needed */
  overflow-y: auto;
  padding: 5px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.ingredient-add-button {
  padding: 6px 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.ingredient-add-button:hover {
  background-color: #0056b3;
}

.ingredient-add-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.selected-ingredients-list {
  list-style-type: none;
  padding: 0;
}

.selected-ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #fdfdfd;
}

.selected-ingredient-item span {
  flex-grow: 1;
}

.ingredient-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.ingredient-controls .ingredient-quantity {
  width: 70px;
  padding: 6px;
}

.ingredient-controls .ingredient-unit {
  padding: 6px;
  min-width: 80px;
}

.ingredient-controls .ingredient-divisible-label {
  font-weight: normal;
  font-size: 0.9em;
  display: flex;
  align-items: center;
}

.ingredient-controls .remove-ingredient-button {
  padding: 6px 10px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.ingredient-controls .remove-ingredient-button:hover {
  background-color: #c82333;
}

.feedback-message {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
}

.feedback-message.success-message {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.feedback-message.error-message {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.submit-recipe-button {
  display: block;
  width: 100%;
  padding: 12px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 20px;
}

.submit-recipe-button:hover {
  background-color: #218838;
}

.submit-recipe-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.ingredient-quantity-input {
  width: 80px; /* Or any specific width you prefer */
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 1rem;
  margin-left: 5px; /* Optional: add some space if needed */
}
