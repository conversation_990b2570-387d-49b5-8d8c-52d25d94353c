import React from 'react';
import { useRecipeAdder } from './RecipeAdder.js';
import './RecipeAdder.css';

const RecipeAdder = () => {
  const {
    name,
    instructions,
    imagePreview,
    // months, // Removed as it's not used directly in the JSX
    type,
    diet,
    servings,
    category,
    // divisible, // Removed as it's not used directly in the JSX
    selectedIngredients,
    searchTerm,
    ingredientAmountToAdd, // Destructure new state
    filteredIngredients,
    feedbackMessage,
    isSubmitting,
    ingredientsLoading,
    ingredientsError,
    handleInputChange,
    handleNameBlur, // Destructure new handler
    // handleSeasonChange, // Removed as it's not used directly in the JSX
    handleIngredientSearch,
    handleIngredientAmountChange, // Destructure new handler
    handleAddIngredient,
    handleRemoveIngredient,
    handleIngredientQuantityChange,
    handleIngredientUnitChange,
    // handleIngredientDivisibleChange, // Removed as it's not used directly in the JSX
    handleSubmit,
    RECIPE_TYPES,
    RECIPE_DIETS,
    RECIPE_CATEGORIES,
    // MONTHS: availableMonths, // Removed as it's not used directly in the JSX
    MEASUREMENT_UNITS,
  } = useRecipeAdder();

  return (
    <div className="recipe-adder-container">
      <h2>Add New Recipe</h2>
      <form onSubmit={handleSubmit} className="recipe-adder-form">
        {/* Recipe Details */}
        <div className="form-section">
          <h3>Recipe Details</h3>
          <div className="form-group">
            <label htmlFor="name">Recipe Name:</label>
            <input
              type="text"
              id="name"
              name="name"
              value={name}
              onChange={handleInputChange}
              onBlur={handleNameBlur} // Add onBlur event
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="instructions">Instructions:</label>
            <textarea
              id="instructions"
              name="instructions"
              value={instructions}
              onChange={handleInputChange}
              rows="5"
            />
          </div>
          <div className="form-group">
            <label htmlFor="image">Recipe Image:</label>
            <input
              type="file"
              id="image"
              name="image"
              accept="image/*"
              onChange={handleInputChange}
            />
            {imagePreview && (
              <img src={imagePreview} alt="Recipe preview" className="image-preview" />
            )}
          </div>
          <div className="form-group">
            <label htmlFor="type">Type:</label>
            <select id="type" name="type" value={type} onChange={handleInputChange}>
              {RECIPE_TYPES.map(t => <option key={t} value={t}>{t}</option>)}
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="diet">Diet:</label>
            <select id="diet" name="diet" value={diet} onChange={handleInputChange}>
              {RECIPE_DIETS.map(d => <option key={d} value={d}>{d}</option>)}
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="category">Category:</label>
            <select id="category" name="category" value={category} onChange={handleInputChange}>
              {RECIPE_CATEGORIES.map(c => <option key={c} value={c}>{c}</option>)}
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="servings">Servings:</label>
            <input
              type="number"
              id="servings"
              name="servings"
              value={servings}
              onChange={handleInputChange}
              min="1"
            />
          </div>
        </div>

        {/* Ingredient Selection */}
        <div className="form-section">
          <h3>Ingredients</h3>
          <div className="form-group">
            <label htmlFor="ingredient-search">Search Ingredients:</label>
            <input
              type="text"
              id="ingredient-search"
              placeholder="Search for ingredients..."
              value={searchTerm}
              onChange={handleIngredientSearch}
            />
          </div>
          <div className="form-group"> {/* New form group for amount input */}
            <label htmlFor="ingredient-amount">Amount:</label>
            <input
              type="text" /* Changed to text for free input */
              id="ingredient-amount"
              name="ingredientAmount"
              value={ingredientAmountToAdd}
              onChange={handleIngredientAmountChange}
              placeholder="e.g. 0.5 or 1" /* Updated placeholder */
              className="ingredient-quantity-input" /* Optional: for specific styling */
              inputMode="decimal" /* Hint for mobile keyboards */
            />
          </div>
          {ingredientsLoading && <p>Loading ingredients...</p>}
          {ingredientsError && <p className="error-message">Error loading ingredients: {ingredientsError}</p>}
          <div className="ingredient-search-results">
            {filteredIngredients.slice(0, 10).map(ingredient => ( // Limit display for performance
              <button
                type="button"
                key={ingredient.id}
                onClick={() => handleAddIngredient(ingredient, ingredientAmountToAdd)}
                className="ingredient-add-button"
                disabled={selectedIngredients.some(si => si.ingredientId === ingredient.id)}
              >
                {ingredient.name}
              </button>
            ))}
          </div>

          <h4>Selected Ingredients:</h4>
          {selectedIngredients.length === 0 && <p>No ingredients selected yet.</p>}
          <ul className="selected-ingredients-list">
            {selectedIngredients.map(ing => (
              <li key={ing.ingredientId} className="selected-ingredient-item">
                <span>{ing.name}</span>
                <div className="ingredient-controls">
                  <input
                    type="number"
                    value={ing.quantity}
                    onChange={(e) => handleIngredientQuantityChange(ing.ingredientId, e.target.value)}
                    min="0.1"
                    step="0.1"
                    className="ingredient-quantity"
                  />
                  <select
                    value={ing.unit}
                    onChange={(e) => handleIngredientUnitChange(ing.ingredientId, e.target.value)}
                    className="ingredient-unit"
                  >
                    {MEASUREMENT_UNITS.map(unit => <option key={unit} value={unit}>{unit}</option>)}
                  </select>
                  <button type="button" onClick={() => handleRemoveIngredient(ing.ingredientId)} className="remove-ingredient-button">
                    Remove
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>

        {feedbackMessage && (
          <p className={`feedback-message ${feedbackMessage.startsWith('Error') ? 'error-message' : 'success-message'}`}>
            {feedbackMessage}
          </p>
        )}

        <button type="submit" className="submit-recipe-button" disabled={isSubmitting}>
          {isSubmitting ? 'Submitting...' : 'Add Recipe'}
        </button>
      </form>
    </div>
  );
};

export default RecipeAdder;
