import React from 'react';
import './styles/RecipeCard.css'; 

// Import sub-components
import Recipe<PERSON>ardHeader from './components/RecipeCardHeader';
import RecipeImageUploader from './components/RecipeImageUploader';
import RecipeCardBody from './components/RecipeCardBody';
import RecipeCardFooter from './components/RecipeCardFooter';

// Import custom hooks and comparison function
import useRecipeImageUpload from './hooks/useRecipeImageUpload';
import useRecipeInstructions from './hooks/useRecipeInstructions';
import { recipeCardPropsAreEqual } from './hooks/recipeCardComparison'; // Import the comparison function

const RecipeCard = ({ recipe, formatSeasonality, handleEditClick, handleDeleteRecipe, onRecipeUpdate }) => {
  const seasonalityData = formatSeasonality(recipe);
  // const [showSkeleton, setShowSkeleton] = useState(false); // Temporarily set to false

  const {
    currentImage,
    uploadError,
    isUploading,
    onDrop,
  } = useRecipeImageUpload(recipe.image, recipe.id, onRecipeUpdate);

  const {
    instructionsExpanded,
    toggleInstructions,
  } = useRecipeInstructions();


  return (
    <div key={recipe.id} className="recipe-card">
      <RecipeCardHeader recipe={recipe} />
      
      <RecipeImageUploader
        recipeName={recipe.name}
        currentImage={currentImage}
        isUploading={isUploading}
        uploadError={uploadError}
        onDrop={onDrop}
      />
      
      <RecipeCardBody
        recipe={recipe}
        seasonalityData={seasonalityData}
        toggleInstructions={toggleInstructions}
        instructionsExpanded={instructionsExpanded}
      />
      
      <RecipeCardFooter
        recipe={recipe}
        handleEditClick={handleEditClick}
        handleDeleteRecipe={handleDeleteRecipe}
      />
    </div>
  );
};

// The recipeCardPropsAreEqual function is now imported and used here
const MemoizedRecipeCard = React.memo(RecipeCard, recipeCardPropsAreEqual);

export default MemoizedRecipeCard;
