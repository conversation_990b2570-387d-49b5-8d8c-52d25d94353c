import React from 'react';
import { useDropzone } from 'react-dropzone';
import '../styles/RecipeImageUploader.css';

const RecipeImageUploader = ({ 
  recipeName, 
  currentImage, 
  isUploading, 
  uploadError, 
  onDrop 
}) => {
  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': [],
      'image/png': [],
      'image/gif': [],
      'image/webp': [],
    },
    multiple: false,
  });

  return (
    <>
      <div
        {...getRootProps()}
        className={`recipe-image-container ${isDragActive ? 'drag-active' : ''} ${isDragReject ? 'drag-reject' : ''}`}
      >
        <input {...getInputProps()} />
        {currentImage ? (
          <img src={currentImage} alt={recipeName} className="recipe-image" />
        ) : (
          <div className="recipe-image-placeholder">
            {isUploading && <p>Uploading...</p>}
            {!isUploading && isDragActive && !isDragReject && <p>Drop the image here ...</p>}
            {!isUploading && isDragReject && <p>Unsupported file type.</p>}
            {!isUploading && !isDragActive && <p>Drag 'n' drop an image here, or click to select</p>}
          </div>
        )}
      </div>
      {uploadError && <p className="recipe-upload-error">{uploadError}</p>}
    </>
  );
};

export default RecipeImageUploader;