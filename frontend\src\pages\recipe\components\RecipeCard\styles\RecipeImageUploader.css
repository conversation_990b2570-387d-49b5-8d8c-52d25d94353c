.recipe-image-container {
  width: 100%;
  height: 180px; /* Adjust as needed */
  border: 2px dashed #ccc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease, background-color 0.3s ease;
  margin-bottom: 10px;
  position: relative; /* For positioning image or placeholder */
  overflow: hidden; /* Ensures image fits within rounded corners */
}

.recipe-image-container.drag-active {
  border-color: #007bff;
  background-color: #f0f8ff; /* Light blue background */
}

.recipe-image-container.drag-reject {
  border-color: #dc3545;
  background-color: #fff0f1; /* Light red background */
}

.recipe-image-container img.recipe-image {
  width: 100%;
  height: 100%; /* Make image fill the container */
  object-fit: contain; /* Changed from cover to contain */
  display: block; /* Remove extra space below image */
}

.recipe-image-placeholder {
  width: 100%;
  height: 100%; /* Placeholder fills the container */
  display: flex;
  flex-direction: column; /* Stack text elements */
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #888;
  padding: 10px; /* Add some padding for text */
}

.recipe-image-placeholder p {
  margin: 5px 0;
}

.recipe-upload-error {
  color: #dc3545; /* Bootstrap danger color */
  font-size: 0.9em;
  margin-top: -5px; /* Adjust to align nicely below the dropzone */
  margin-bottom: 10px;
  text-align: center;
}