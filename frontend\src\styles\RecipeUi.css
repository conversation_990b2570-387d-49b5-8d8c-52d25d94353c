.recipe-page {
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
  }

  .recipe-header {
    padding: 1rem;
    background-color: #2a9d8f;
    color: white;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  }
  
  .recipe-list,
  .add-recipe-form {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    flex: 1;
    min-width: 300px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .recipe-items {
    list-style: none;
    padding: 0;
  }
  
  .recipe-item {
    border: 4px solid #6a994e;
    background-color: #f8f9fa;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 4px;
  }

  .recipe-item.not-divisible {
    border: 4px solid #ff0000;
  }
  
  .recipe-item h3 {
    margin-top: 0;
    color: #333;
  }

  .recipe-creation-area {
    display: flex;
    gap: 20px;
    padding: 20px;
    width: calc(100% - 40px);
    margin: 0 auto;
    min-height: 100vh;
    background: linear-gradient(to bottom,
    #87CEEB 0%, /* Sky blue at the top */
    #87CEEB 60%, /* Sky blue continues to 60% of the height */
    #4a7c59 60%,
    #4a7c59 100%);
  }

  .ingredient-groups {
    flex: 1;
  }

  .selected-ingredient {
    display: flex;
    align-items: center;
    padding: 10px;
    gap: 10px;
    border: 1px solid #a2f8a2;
    border-radius: 4px;
    margin-bottom: 5px;
  }

  .add-recipe-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .buttons-for-type{
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .ingredient-button-vegetable{
    background-color:green;
    color :white;
    
  }

  .ingredient-button-pantry{
    background-color: yellow;
    color: black;
  }

  .recipe-meta {
    display: flex;
    gap: 20px;
    margin-top: 20px;
  }

  .recipe-meta span {
    font-size: 0.9rem;
  }
  
  .recipe-seasonality {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
    margin-bottom: 15px;
  }
  
  .no-seasonality {
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
  }
  
  /* Season indicators */
  .season-indicator {
    display: flex;
    gap: 2px;
    flex-wrap: nowrap;
  }
  
  .month-indicator-container {
    position: relative;
    display: inline-block;
  }
  
  .month-indicator {
    width: 10px;
    height: 10px;
    border-radius: 2px;
    background-color: #e0e0e0; /* Default gray color */
    display: inline-block;
    border: 1px solid #ccc;
  }
  
  /* Winter (Dec-Feb) */
  .month-active-12, .month-active-1, .month-active-2 {
    background-color: #a8dadc; /* Blue */
    border-color: #457b9d;
  }
  
  /* Spring (Mar-May) */
  .month-active-3, .month-active-4, .month-active-5 {
    background-color: #6a994e; /* Green */
    border-color: #386641;
  }
  
  /* Summer (Jun-Aug) */
  .month-active-6, .month-active-7, .month-active-8 {
    background-color: #ffb703; /* Yellow */
    border-color: #fb8500;
  }
  
  /* Fall (Sep-Nov) */
  .month-active-9, .month-active-10, .month-active-11 {
    background-color: #e76f51; /* Orange */
    border-color: #bc4b27;
  }
  
  .month-tooltip {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 10px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s;
    white-space: nowrap;
    z-index: 10;
  }
  
  .month-indicator-container:hover .month-tooltip {
    opacity: 1;
    visibility: visible;
  }

  /* Serving size buttons and refresh button */
  .serving-size-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
  }

  .serving-button {
    padding: 8px 15px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .serving-button.active {
    background-color: #6a994e;
    color: white;
    border-color: #386641;
  }

  .original-quantity {
    font-size: 0.8em;
    color: #666;
    margin-left: 8px;
    font-style: italic;
  }

  /* Recipe UI Styles */
  .recipe-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .subtitle {
    color: #666;
    margin-bottom: 20px;
  }

  .error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    border-left: 4px solid #c62828;
  }

  .recipe-controls {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px;
  }

  .add-recipe-btn {
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .add-recipe-btn:hover {
    background-color: #388e3c;
  }

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .filter-group label {
    font-size: 0.9rem;
    color: #555;
  }

  .filter-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    min-width: 150px;
  }

  /* Recipe List */
  .recipe-list {
    margin-top: 30px;
  }

  .recipe-list h2 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
  }

  .loading {
    text-align: center;
    padding: 30px;
    color: #666;
    font-size: 1.1rem;
  }

  .no-recipes {
    text-align: center;
    padding: 30px;
    background-color: #f9f9f9;
    border-radius: 8px;
  }

  .no-recipes p {
    color: #666;
    font-size: 1.1rem;
  }

  .recipe-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
  }

  .recipe-card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background-color: white;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .recipe-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }

  .recipe-header {
    background-color: #f0f7ff;
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
  }

  .recipe-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 8px;
  }

  .recipe-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: #555;
  }

  .recipe-type {
    background-color: #e3f2fd;
    padding: 3px 8px;
    border-radius: 4px;
    color: #1565c0;
  }

  .recipe-servings {
    background-color: #fff3e0;
    padding: 3px 8px;
    border-radius: 4px;
    color: #e65100;
  }

  .recipe-divisibility {
    background-color: #f0f0f0;
    padding: 3px 8px;
    border-radius: 4px;
    color: #333;
  }

  .recipe-body {
    padding: 15px;
  }

  .recipe-ingredients {
    margin-bottom: 15px;
  }

  .recipe-ingredients h4,
  .recipe-instructions h4 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 1rem;
    color: #333;
  }

  .recipe-ingredients ul {
    margin: 0;
    padding-left: 20px;
  }

  .recipe-ingredients li {
    margin-bottom: 5px;
  }

  .recipe-instructions p {
    margin: 0;
    line-height: 1.5;
    color: #333;
  }

  .recipe-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #e0e0e0;
  }

  .diet-tag {
    background-color: #e8f5e9;
    color: #2e7d32;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
  }

  .recipe-actions {
    display: flex;
    gap: 10px;
  }

  .edit-btn,
  .delete-btn {
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .edit-btn {
    background-color: #2196f3;
    color: white;
  }

  .edit-btn:hover {
    background-color: #1976d2;
  }

  .delete-btn {
    background-color: #f44336;
    color: white;
  }

  .delete-btn:hover {
    background-color: #d32f2f;
  }

  /* Responsive styles */
  @media (max-width: 768px) {
    .recipe-cards {
      grid-template-columns: 1fr;
    }
    
    .recipe-controls {
      flex-direction: column;
      align-items: stretch;
    }
    
    .filter-container {
      flex-direction: column;
    }
    
    .filter-group {
      width: 100%;
    }
  }
