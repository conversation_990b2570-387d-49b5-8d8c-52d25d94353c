from rest_framework import serializers
from ...models import Ingredient

class IngredientSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ingredient  
        fields = '__all__'

# Add a more lightweight serializer for lists of ingredients
class IngredientListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ingredient
        # Include only the most commonly needed fields in list views
        fields = ['id', 'name', 'has_seasonality', 'type', 'divisible_by_measurement', 
                 'divisible_by_int', 'bought_by', 'bought_by_amount'] 